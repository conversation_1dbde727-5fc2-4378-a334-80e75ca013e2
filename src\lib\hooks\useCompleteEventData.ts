/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import { events } from '../services/events';
import { CompleteEventData } from '../store/event';
import { toast } from 'react-toastify';

/**
 * Custom hook to fetch complete event data by ID
 * This hook automatically fetches complete event details when eventId changes
 */
export const useCompleteEventData = (eventId: string | null | undefined) => {
  const [completeEventData, setCompleteEventData] = useState<CompleteEventData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!eventId) {
      setCompleteEventData(null);
      setError(null);
      return;
    }

    const fetchCompleteEventData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await events.getEventByID(eventId);
        setCompleteEventData(response.data);
      } catch (err: any) {
        const errorMessage = err?.response?.data?.message || 'Failed to fetch complete event details';
        setError(errorMessage);
        toast.error(errorMessage);
        console.error('Error fetching complete event data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCompleteEventData();
  }, [eventId]);

  // Function to manually refetch the data
  const refetch = async () => {
    if (!eventId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await events.getEventByID(eventId);
      setCompleteEventData(response.data);
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || 'Failed to fetch complete event details';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Error refetching complete event data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    completeEventData,
    isLoading,
    error,
    refetch,
  };
};
